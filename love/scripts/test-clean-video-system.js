#!/usr/bin/env node

/**
 * 测试简洁2K视频系统
 * 验证新的视频加载机制是否正常工作
 */

console.log('🎬 测试简洁2K视频系统');
console.log('=====================================\n');

// 测试本地视频文件可访问性
async function testVideoAccess() {
    console.log('🔍 测试本地视频文件可访问性...\n');
    
    const testUrls = [
        {
            name: '主要2K视频',
            url: 'http://localhost:1314/background/home/<USER>'
        },
        {
            name: '备用2K视频',
            url: 'http://localhost:1314/background/cloudinary-ready/home.mp4'
        }
    ];
    
    for (const urlConfig of testUrls) {
        console.log(`📹 测试 ${urlConfig.name}:`);
        console.log(`🔗 URL: ${urlConfig.url}`);
        
        try {
            const response = await fetch(urlConfig.url, { method: 'HEAD' });
            const contentLength = response.headers.get('content-length');
            const contentType = response.headers.get('content-type');
            
            if (response.ok) {
                console.log(`✅ 状态: ${response.status} - 可访问`);
                console.log(`📊 大小: ${contentLength ? (parseInt(contentLength) / 1024 / 1024).toFixed(2) + 'MB' : 'Unknown'}`);
                console.log(`📄 类型: ${contentType || 'Unknown'}`);
            } else {
                console.log(`❌ 状态: ${response.status} - 不可访问`);
            }
        } catch (error) {
            console.log(`❌ 错误: ${error.message}`);
        }
        
        console.log('---');
    }
}

// 生成浏览器测试代码
function generateBrowserTestCode() {
    console.log('\n📋 浏览器控制台测试代码:');
    console.log('=====================================');
    
    const browserCode = `
// 简洁2K视频系统测试代码 - 在love.yuh.cool控制台执行
function test2KVideoSystem() {
    console.log('🎬 测试简洁2K视频系统...');
    
    // 检查视频配置
    console.log('📋 视频配置:', window.VIDEO_CONFIG);
    
    // 检查视频元素
    const videos = document.querySelectorAll('video');
    console.log(\`🎥 找到 \${videos.length} 个视频元素\`);
    
    videos.forEach((video, index) => {
        console.log(\`📹 视频\${index + 1}:\`);
        console.log(\`  URL: \${video.src}\`);
        console.log(\`  分辨率: \${video.videoWidth}x\${video.videoHeight}\`);
        console.log(\`  状态: \${video.readyState >= 4 ? '可播放' : '未就绪'}\`);
        console.log(\`  自动播放: \${video.autoplay}\`);
        console.log(\`  静音: \${video.muted}\`);
        console.log(\`  循环: \${video.loop}\`);
        
        // 检查是否为2K分辨率
        if (video.videoWidth >= 2560 && video.videoHeight >= 1440) {
            console.log('  🎯 ✅ 确认为2K分辨率！');
        } else if (video.videoWidth > 0) {
            console.log(\`  🎯 ⚠️ 分辨率 \${video.videoWidth}x\${video.videoHeight} 不是2K\`);
        } else {
            console.log('  🎯 ❌ 视频尚未加载');
        }
    });
    
    // 检查容器
    const container = document.getElementById('video-background-container');
    console.log(\`📦 视频容器: \${container ? '存在' : '不存在'}\`);
    if (container) {
        console.log(\`  子元素数量: \${container.children.length}\`);
    }
    
    // 检查加载遮罩
    const loadingOverlay = document.getElementById('loadingOverlay');
    console.log(\`🎭 加载遮罩: \${loadingOverlay ? '存在' : '不存在'}\`);
    if (loadingOverlay) {
        console.log(\`  是否隐藏: \${loadingOverlay.classList.contains('hidden')}\`);
    }
}

// 强制重新创建2K视频
function recreate2KVideo() {
    console.log('🔄 强制重新创建2K视频...');
    
    // 移除现有视频
    const existingVideos = document.querySelectorAll('video');
    existingVideos.forEach(video => video.remove());
    
    // 重新创建视频
    const container = document.getElementById('video-background-container');
    if (!container) {
        console.error('❌ 视频容器不存在');
        return;
    }
    
    const video = document.createElement('video');
    video.id = 'background-video';
    video.autoplay = true;
    video.muted = true;
    video.loop = true;
    video.playsInline = true;
    video.preload = 'auto';
    
    video.style.cssText = \`
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: -1;
    \`;
    
    video.addEventListener('loadedmetadata', function() {
        console.log(\`📊 重新创建的视频分辨率: \${this.videoWidth}x\${this.videoHeight}\`);
        if (this.videoWidth >= 2560) {
            console.log('✅ 2K视频重新创建成功！');
        } else {
            console.warn(\`⚠️ 重新创建的视频分辨率仍不是2K: \${this.videoWidth}x\${this.videoHeight}\`);
        }
    });
    
    video.addEventListener('error', function(e) {
        console.error('❌ 重新创建的视频加载失败:', e);
    });
    
    video.src = window.VIDEO_CONFIG.videoUrl;
    container.appendChild(video);
    
    console.log('🎬 视频已重新创建');
}

// 检查视频文件可访问性
async function checkVideoFileAccess() {
    console.log('🔍 检查视频文件可访问性...');
    
    const urls = [
        window.VIDEO_CONFIG.videoUrl,
        window.VIDEO_CONFIG.fallbackUrl
    ];
    
    for (const url of urls) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            console.log(\`\${response.ok ? '✅' : '❌'} \${url}: \${response.status}\`);
        } catch (error) {
            console.log(\`❌ \${url}: \${error.message}\`);
        }
    }
}

// 立即执行测试
test2KVideoSystem();
checkVideoFileAccess();

console.log('💡 如果视频不是2K，请执行: recreate2KVideo()');
`;
    
    console.log(browserCode);
    console.log('=====================================\n');
}

// 主测试函数
async function runCleanVideoSystemTest() {
    console.log('🧪 开始测试简洁2K视频系统...\n');
    
    // 1. 测试视频文件可访问性
    await testVideoAccess();
    
    // 2. 生成浏览器测试代码
    generateBrowserTestCode();
    
    console.log('📝 测试总结:');
    console.log('1. ✅ 已删除所有复杂的视频加载代码');
    console.log('2. ✅ 创建了简洁的2K视频背景系统');
    console.log('3. ✅ 视频直接通过JavaScript动态创建');
    console.log('4. ✅ 包含错误处理和备用视频机制');
    console.log('5. 🎯 现在应该能正确显示2K分辨率视频');
    
    console.log('\n🚀 下一步操作:');
    console.log('1. 访问 love.yuh.cool');
    console.log('2. 打开浏览器开发者工具 (F12)');
    console.log('3. 在控制台执行上述测试代码');
    console.log('4. 检查视频分辨率是否为2560x1440');
    console.log('5. 如有问题，执行 recreate2KVideo() 重新创建');
    
    console.log('\n✅ 简洁2K视频系统测试完成！');
}

// 执行测试
if (require.main === module) {
    runCleanVideoSystemTest().catch(console.error);
}

module.exports = { runCleanVideoSystemTest, testVideoAccess };
