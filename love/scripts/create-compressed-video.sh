#!/bin/bash

# 创建压缩版2K视频脚本
# 目标：创建一个较小但仍保持2K分辨率的视频文件

echo "🎬 创建压缩版2K视频..."

# 输入和输出文件
INPUT_FILE="background/home/<USER>"
OUTPUT_FILE="background/home/<USER>"

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "❌ 输入文件不存在: $INPUT_FILE"
    exit 1
fi

echo "📹 输入文件: $INPUT_FILE"
echo "📹 输出文件: $OUTPUT_FILE"

# 获取原始视频信息
echo "📊 原始视频信息:"
ffprobe -v quiet -print_format json -show_streams "$INPUT_FILE" | jq -r '.streams[0] | "分辨率: \(.width)x\(.height), 码率: \(.bit_rate), 时长: \(.duration)秒"'

# 创建压缩版本 - 保持2K分辨率但降低码率
echo "🔄 开始压缩..."
ffmpeg -i "$INPUT_FILE" \
    -c:v libx264 \
    -preset medium \
    -crf 28 \
    -vf "scale=2560:1440" \
    -r 30 \
    -c:a aac \
    -b:a 128k \
    -movflags +faststart \
    -y "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    echo "✅ 压缩完成!"
    
    # 显示压缩后的信息
    echo "📊 压缩后视频信息:"
    ffprobe -v quiet -print_format json -show_streams "$OUTPUT_FILE" | jq -r '.streams[0] | "分辨率: \(.width)x\(.height), 码率: \(.bit_rate), 时长: \(.duration)秒"'
    
    # 显示文件大小对比
    echo "📈 文件大小对比:"
    echo "原始文件: $(ls -lh $INPUT_FILE | awk '{print $5}')"
    echo "压缩文件: $(ls -lh $OUTPUT_FILE | awk '{print $5}')"
    
    echo "🎯 压缩版2K视频创建成功: $OUTPUT_FILE"
else
    echo "❌ 压缩失败"
    exit 1
fi
