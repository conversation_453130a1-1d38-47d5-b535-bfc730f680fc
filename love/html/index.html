<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们美好爱情故事的专属网站，包含恋爱天数计算器和生日倒计时">
    <meta name="keywords" content="爱情, 情侣, 纪念日, 生日, 恋爱天数">
    <title>Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">

    <!-- 防止白色闪屏的关键CSS -->
    <style>
        html, body {
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4c1d95 75%,
                #6b21a8 100%
            ) !important;
            margin: 0;
            padding: 0;
        }
    </style>

    <!-- 配置文件 - 必须在其他脚本之前加载 -->
    <script src="/config.js"></script>

    <!-- 立即执行的紧急修复脚本 -->
    <script>
        // 立即创建紧急VideoManager，防止初始化失败
        console.log('🚨 立即执行紧急修复...');

        // 生成正确的视频URL函数 - 2K修复版本
        function generateEmergencyVideoUrl(pageKey) {
            const videoConfigs = {
                'INDEX': {
                    cloudName: 'dcglebc2w',
                    publicId: 'love-website/home'
                }
            };

            const config = videoConfigs[pageKey];
            if (!config) return null;

            // 简单的设备检测
            const screenWidth = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);

            // 强制使用2K分辨率，使用最简单的缩放参数
            let transforms;
            if (isMobile) {
                // 移动端使用1920宽度
                transforms = 'w_1920';
                console.log(`📱 ${pageKey}: 使用移动设备高清质量 (1920宽度)`);
            } else {
                // 桌面端强制2K质量
                transforms = 'w_2560';
                console.log(`🖥️ ${pageKey}: 使用桌面设备2K质量 (2560宽度)`);
            }

            const url = `https://res.cloudinary.com/${config.cloudName}/video/upload/${transforms}/${config.publicId}.mp4`;
            console.log(`📹 2K修复版视频URL: ${url}`);

            // 备用URL（原始2K质量，无压缩）
            const fallbackUrl = `https://res.cloudinary.com/${config.cloudName}/video/upload/${config.publicId}.mp4`;
            console.log(`📹 备用2K视频URL: ${fallbackUrl}`);

            return url;
        }

        // 生成备用URL函数
        function generateFallbackVideoUrl(pageKey) {
            const videoConfigs = {
                'INDEX': {
                    cloudName: 'dcglebc2w',
                    publicId: 'love-website/home'
                }
            };

            const config = videoConfigs[pageKey];
            if (!config) return null;

            // 返回无变换参数的原始URL
            return `https://res.cloudinary.com/${config.cloudName}/video/upload/${config.publicId}.mp4`;
        }

        // 立即创建紧急VideoManager
        window.VideoManager = {
            isInitialized: true,

            async loadVideo(pageKey, videoConfig) {
                console.log(`🚨 紧急VideoManager加载视频: ${pageKey}`);

                const video = document.createElement('video');
                video.autoplay = true;
                video.muted = true;
                video.loop = true;
                video.playsInline = true;
                video.preload = 'auto';
                video.style.opacity = '0';
                video.style.transition = 'opacity 1s ease-in-out';

                const url = generateEmergencyVideoUrl(pageKey);
                if (!url) {
                    throw new Error(`无法生成${pageKey}的视频URL`);
                }

                return new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('视频加载超时'));
                    }, 15000);

                    video.addEventListener('canplaythrough', () => {
                        clearTimeout(timeout);
                        video.style.opacity = '1';
                        console.log(`✅ 紧急VideoManager: ${pageKey}视频加载成功`);
                        resolve(video);
                    });

                    video.addEventListener('error', (e) => {
                        console.warn(`⚠️ 主URL加载失败，尝试备用URL: ${e.message}`);

                        // 尝试备用URL
                        const fallbackUrl = generateFallbackVideoUrl(pageKey);
                        if (fallbackUrl && video.src !== fallbackUrl) {
                            console.log(`🔄 尝试备用URL: ${fallbackUrl}`);
                            video.src = fallbackUrl;
                            video.load();
                            return; // 不要reject，给备用URL一个机会
                        }

                        clearTimeout(timeout);
                        console.error(`❌ 紧急VideoManager: ${pageKey}视频加载完全失败:`, e);
                        reject(new Error(`视频加载失败: ${e.message || '未知错误'}`));
                    });

                    video.src = url;
                    video.load();
                });
            },

            pauseAllVideos() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    if (!video.paused) video.pause();
                });
            },

            resumeCurrentVideo() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    if (video.paused) {
                        video.play().catch(e => console.log('自动播放被阻止'));
                    }
                });
            },

            startPreloading() {
                console.log('📦 紧急模式：预加载功能已禁用');
                return Promise.resolve();
            },

            cleanupPreloadCache() {
                console.log('🗑️ 紧急模式：缓存清理功能已禁用');
            }
        };

        // 提供全局修复函数
        window.generateWorkingVideoUrl = generateEmergencyVideoUrl;
        window.generateFallbackVideoUrl = generateFallbackVideoUrl;
        window.emergencyVideoFix = function() {
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
                const url = generateEmergencyVideoUrl('INDEX');
                if (url && video.src !== url) {
                    console.log(`🔄 修复视频 ${index + 1}: ${url}`);

                    // 添加错误处理，自动尝试备用URL
                    video.addEventListener('error', function(e) {
                        const fallbackUrl = generateFallbackVideoUrl('INDEX');
                        if (fallbackUrl && video.src !== fallbackUrl) {
                            console.log(`🔄 尝试备用URL: ${fallbackUrl}`);
                            video.src = fallbackUrl;
                            video.load();
                        }
                    }, { once: true });

                    video.src = url;
                    video.load();
                }
            });
        };

        console.log('✅ 紧急VideoManager已创建');

        // 添加视频尺寸显示功能
        function createVideoInfoDisplay() {
            // 移除现有的显示
            const existing = document.getElementById('video-info-display');
            if (existing) existing.remove();

            const infoDiv = document.createElement('div');
            infoDiv.id = 'video-info-display';
            infoDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                z-index: 10000;
                max-width: 300px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;

            document.body.appendChild(infoDiv);
            updateVideoInfoDisplay();
            return infoDiv;
        }

        // 更新视频信息显示
        function updateVideoInfoDisplay() {
            const infoDiv = document.getElementById('video-info-display');
            if (!infoDiv) return;

            const videos = document.querySelectorAll('video');
            const screenWidth = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);

            let info = `📱 设备信息:\n`;
            info += `类型: ${isMobile ? 'Mobile' : 'Desktop'} (${screenWidth}x${window.innerHeight})\n`;
            info += `浏览器: ${navigator.userAgent.split(' ')[0]}\n\n`;

            info += `🎥 视频信息 (${videos.length}个):\n`;

            videos.forEach((video, index) => {
                const resolution = video.videoWidth && video.videoHeight ?
                    `${video.videoWidth}x${video.videoHeight}` : '未加载';
                const status = video.readyState >= 4 ? '✅ 可播放' :
                              video.readyState >= 1 ? '⏳ 加载中' : '❌ 未加载';

                info += `视频${index + 1}: ${resolution} ${status}\n`;

                if (video.src) {
                    const urlParts = video.src.split('/');
                    const transformPart = urlParts.find(part => part.includes('c_scale') || part.includes('q_auto'));
                    info += `参数: ${transformPart || '无'}\n`;
                }
                info += '\n';
            });

            info += `⏰ 更新时间: ${new Date().toLocaleTimeString()}`;

            infoDiv.textContent = info;
        }

        // 页面加载完成后创建信息显示
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createVideoInfoDisplay);
        } else {
            createVideoInfoDisplay();
        }

        // 定期更新信息显示
        setInterval(updateVideoInfoDisplay, 2000);

        // 监听视频事件
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('video').forEach(video => {
                video.addEventListener('loadedmetadata', updateVideoInfoDisplay);
                video.addEventListener('canplaythrough', updateVideoInfoDisplay);
            });
        });

        // 导出全局测试函数
        window.showVideoInfo = function() {
            const videos = document.querySelectorAll('video');
            console.log('🎥 当前视频状态:');
            videos.forEach((video, index) => {
                console.log(`视频${index + 1}:`, {
                    URL: video.src,
                    分辨率: video.videoWidth && video.videoHeight ?
                        `${video.videoWidth}x${video.videoHeight}` : '未加载',
                    状态: video.readyState >= 4 ? '可播放' : '未就绪',
                    readyState: video.readyState
                });
            });
        };

        window.testVideoUrl = async function(url) {
            console.log('🧪 测试URL:', url);
            try {
                const response = await fetch(url, { method: 'HEAD' });
                console.log(`${response.ok ? '✅' : '❌'} 状态: ${response.status}`);
                return response.ok;
            } catch (error) {
                console.log('❌ 错误:', error.message);
                return false;
            }
        };

        window.fixVideoNow = function() {
            console.log('🔧 立即修复视频...');
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
                const newUrl = 'https://res.cloudinary.com/dcglebc2w/video/upload/c_scale,w_2560/love-website/home.mp4';
                console.log(`修复视频${index + 1}: ${newUrl}`);
                video.src = newUrl;
                video.load();
            });
        };
    </script>

    <!-- 动态样式管理器 -->
    <script src="/dynamic-styles.js"></script>

    <link rel="stylesheet" href="/style.css?v=green-buttons-020">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 自定义样式：让留言时间字体与用户名保持一致 + 留言板标题闪光效果 + 折叠按钮透明背景 -->
    <style>
        .message-date {
            font-family: 'Dancing Script', cursive !important;
            font-weight: 700 !important;
        }
        
        /* 留言板标题闪闪发光效果 */
        .messages-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        @keyframes sparkle {
            0%, 100% {
                background-position: 0% 50%;
                filter: brightness(1);
            }
            25% {
                background-position: 25% 50%;
                filter: brightness(1.2);
            }
            50% {
                background-position: 100% 50%;
                filter: brightness(1.5);
            }
            75% {
                background-position: 75% 50%;
                filter: brightness(1.2);
            }
        }
        
        /* 折叠按钮透明背景 */
        .collapse-btn {
            background: transparent !important;
            border: 2px solid rgba(103, 232, 249, 0.6) !important;
        }
        
        .collapse-btn:hover {
            background: rgba(103, 232, 249, 0.1) !important;
            border-color: #67e8f9 !important;
            color: #67e8f9 !important;
        }
        
        /* 增大留言内容的字体并确保字体正确应用 */
        .message-content {
            font-size: 1.4rem !important;
            font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif !important;
        }
        
        /* 增大输入框的字体和占位符文字，确保输入文字颜色与留言栏一致 */
        #messageText {
            font-size: 1.4rem !important;
            color: #d4af37 !important; /* 淡金色，与留言栏文字颜色一致 */
        }
        
        /* 确保输入框中输入的文字颜色与留言显示颜色完全一致 */
        #messageText,
        #messageText:focus,
        #messageText:active,
        textarea#messageText {
            color: #d4af37 !important; /* 淡金色，强制覆盖所有其他颜色设置 */
            caret-color: #d4af37 !important; /* 光标颜色也设为淡金色 */
        }
        
        #messageText::placeholder {
            font-size: 1.4rem !important;
        }
        
        /* 修复倾斜效果：默认不倾斜，悬停时才倾斜 */
        .counter-card h2 {
            transform: rotate(0deg) !important;
        }
        
        .counter-card h2:hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text) {
            transform: rotate(0deg) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text):hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        /* "写下爱的话语"闪闪发光效果 */
        .form-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        /* 底部四个回忆板块不同颜色闪闪发光效果 */
        
        /* 在一起的日子 - 蓝紫色 */
        .memory-item:nth-child(1) .memory-placeholder p {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6, #8b5cf6) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
        }
        
        .memory-item:nth-child(1) .memory-placeholder i {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6, #8b5cf6) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
        }
        
        /* 纪念日 - 金橙色 */
        .memory-item:nth-child(2) .memory-placeholder p {
            background: linear-gradient(45deg, #f59e0b, #f97316, #f59e0b, #f97316) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(245, 158, 11, 0.5), 0 0 20px rgba(249, 115, 22, 0.3) !important;
        }
        
        .memory-item:nth-child(2) .memory-placeholder i {
            background: linear-gradient(45deg, #f59e0b, #f97316, #f59e0b, #f97316) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(245, 158, 11, 0.5), 0 0 20px rgba(249, 115, 22, 0.3) !important;
        }
        
        /* 纪念物 - 绿青色 */
        .memory-item:nth-child(3) .memory-placeholder p {
            background: linear-gradient(45deg, #10b981, #06b6d4, #10b981, #06b6d4) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(6, 182, 212, 0.3) !important;
        }
        
        .memory-item:nth-child(3) .memory-placeholder i {
            background: linear-gradient(45deg, #10b981, #06b6d4, #10b981, #06b6d4) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(6, 182, 212, 0.3) !important;
        }
        
        /* 每一次相遇 - 红粉色 */
        .memory-item:nth-child(4) .memory-placeholder p {
            background: linear-gradient(45deg, #ef4444, #ec4899, #ef4444, #ec4899) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(236, 72, 153, 0.3) !important;
        }
        
                 .memory-item:nth-child(4) .memory-placeholder i {
             background: linear-gradient(45deg, #ef4444, #ec4899, #ef4444, #ec4899) !important;
             background-size: 400% 400% !important;
             -webkit-background-clip: text !important;
             -webkit-text-fill-color: transparent !important;
             background-clip: text !important;
             animation: sparkle 2s ease-in-out infinite !important;
             text-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(236, 72, 153, 0.3) !important;
         }
         
         /* 底部版权文字闪闪发光效果 - 浪漫红粉色 */
         .footer p {
             background: linear-gradient(45deg, #e91e63, #ff6b9d, #e91e63, #ff6b9d) !important;
             background-size: 400% 400% !important;
             -webkit-background-clip: text !important;
             -webkit-text-fill-color: transparent !important;
             background-clip: text !important;
             animation: sparkle 2s ease-in-out infinite !important;
             text-shadow: 0 0 10px rgba(233, 30, 99, 0.5), 0 0 20px rgba(255, 107, 157, 0.3) !important;
         }
         
                   .footer p i {
              background: linear-gradient(45deg, #e91e63, #ff6b9d, #e91e63, #ff6b9d) !important;
              background-size: 400% 400% !important;
              -webkit-background-clip: text !important;
              -webkit-text-fill-color: transparent !important;
              background-clip: text !important;
              animation: sparkle 2s ease-in-out infinite !important;
              text-shadow: 0 0 10px rgba(233, 30, 99, 0.5), 0 0 20px rgba(255, 107, 157, 0.3) !important;
          }
          
                     /* 美化情话板块 - 透明背景 */
           .quote-card {
               position: relative !important;
               background: transparent !important;
               border: 2px solid rgba(255, 255, 255, 0.3) !important;
               border-radius: 25px !important;
               padding: 35px !important;
               box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 
                          0 0 25px rgba(255, 107, 157, 0.15) !important;
               overflow: hidden !important;
           }
          
          .quote-card::before {
              content: '' !important;
              position: absolute !important;
              top: -50% !important;
              left: -50% !important;
              width: 200% !important;
              height: 200% !important;
              background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%) !important;
              animation: shimmer 4s linear infinite !important;
              pointer-events: none !important;
          }
          
                     /* 右上角换一句按钮 - 透明背景 */
           .quote-btn-top-right {
               position: absolute !important;
               top: 15px !important;
               right: 15px !important;
               background: transparent !important;
               border: 2px solid rgba(103, 232, 249, 0.6) !important;
               border-radius: 20px !important;
               padding: 8px 16px !important;
               font-size: 0.9rem !important;
               color: #67e8f9 !important;
               transition: all 0.3s ease !important;
               z-index: 10 !important;
           }
           
           .quote-btn-top-right:hover {
               background: rgba(103, 232, 249, 0.1) !important;
               border-color: #22d3ee !important;
               color: #22d3ee !important;
               transform: translateY(-2px) scale(1.05) !important;
               box-shadow: 0 8px 25px rgba(103, 232, 249, 0.3) !important;
           }
          
          /* 美化引用图标 */
          .quote-icon {
              color: #67e8f9 !important;
              font-size: 1.8rem !important;
              margin-bottom: 20px !important;
              display: block !important;
              text-shadow: 0 0 15px rgba(103, 232, 249, 0.5) !important;
          }

          /* 隐藏类 */
          .hidden {
              display: none !important;
          }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌸 加载花朵背景中...</div>
        <div class="loading-subtitle">正在为您准备美好的爱情主页</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 花朵视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="auto" id="background-video">
            <source src="/background/home/<USER>" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <div class="container">
        <!-- 可点击的浪漫星星 -->
        <div class="romantic-stars">
            <div class="romantic-star star-variant-1" data-quote-category="confession" style="top: 15%; left: 20%;"></div>
            <div class="romantic-star star-variant-2" data-quote-category="sweet" style="top: 25%; left: 80%;"></div>
            <div class="romantic-star star-variant-3" data-quote-category="promise" style="top: 35%; left: 15%;"></div>
            <div class="romantic-star star-variant-4" data-quote-category="missing" style="top: 45%; left: 85%;"></div>
            <div class="romantic-star star-variant-5" data-quote-category="confession" style="top: 55%; left: 25%;"></div>
            <div class="romantic-star star-variant-6" data-quote-category="sweet" style="top: 65%; left: 75%;"></div>
            <div class="romantic-star star-variant-7" data-quote-category="promise" style="top: 75%; left: 10%;"></div>
            <div class="romantic-star star-variant-8" data-quote-category="missing" style="top: 85%; left: 90%;"></div>
            <!-- 新增更多星星，使用新的颜色变体 -->
            <div class="romantic-star star-variant-9" data-quote-category="confession" style="top: 12%; left: 60%;"></div>
            <div class="romantic-star star-variant-10" data-quote-category="sweet" style="top: 28%; left: 45%;"></div>
            <div class="romantic-star star-variant-11" data-quote-category="promise" style="top: 42%; left: 30%;"></div>
            <div class="romantic-star star-variant-12" data-quote-category="missing" style="top: 58%; left: 70%;"></div>
            <div class="romantic-star star-variant-13" data-quote-category="confession" style="top: 72%; left: 55%;"></div>
            <div class="romantic-star star-variant-14" data-quote-category="sweet" style="top: 88%; left: 35%;"></div>
            <div class="romantic-star star-variant-15" data-quote-category="promise" style="top: 18%; left: 95%;"></div>
            <div class="romantic-star star-variant-16" data-quote-category="missing" style="top: 38%; left: 5%;"></div>
        </div>

        <!-- 浪漫话语模态框 -->
        <div id="romanticModal" class="romantic-modal">
            <div class="romantic-modal-content">
                <div class="romantic-modal-header">
                    <h3>💫 星星的话语</h3>
                    <button class="close-romantic-btn" onclick="closeRomanticModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="romantic-quote-content">
                    <p class="quote-text" id="romanticQuoteText"></p>
                    <div class="quote-attribution">
                        <span class="quote-author" id="romanticQuoteAuthor"></span>
                        <span class="quote-work" id="romanticQuoteWork"></span>
                    </div>
                </div>
                <div class="romantic-modal-footer">
                    <button class="new-quote-btn" onclick="getNewQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                </div>
            </div>
        </div>

        <!-- 流星效果 -->
        <div class="shooting-stars">
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
        </div>

        <!-- 浪漫漂浮元素 -->
        <div class="hearts-container">
            <div class="heart type-1 small"></div>
            <div class="heart type-2 medium"></div>
            <div class="heart type-3 large"></div>
            <div class="heart type-4 small"></div>
            <div class="heart type-5 medium"></div>
            <div class="heart type-6 large"></div>
            <div class="heart type-7 small"></div>
            <div class="heart type-8 medium"></div>
            <div class="heart type-9 large"></div>
            <div class="heart type-10 small"></div>
            <div class="heart type-11 medium"></div>
            <div class="heart type-12 large"></div>
            <div class="heart type-1 medium"></div>
            <div class="heart type-3 small"></div>
            <div class="heart type-5 large"></div>
        </div>

        <!-- Main content -->
        <header class="header">
            <h1 class="title">
                Yu
                <i class="fas fa-heart"></i>
                Wang
            </h1>
            <p class="subtitle">Forever and Always 💕</p>
        </header>

        <main class="main-content">
            <!-- Love counter section -->
            <section class="love-counter">
                <div class="counter-card">
                    <h2>我们在一起已经</h2>
                    <div class="counter-display">
                        <div class="counter-item">
                            <span class="counter-number" id="days">0</span>
                            <span class="counter-label">天</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="hours">0</span>
                            <span class="counter-label">小时</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="minutes">0</span>
                            <span class="counter-label">分钟</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="seconds">0</span>
                            <span class="counter-label">秒</span>
                        </div>
                    </div>
                    <p class="start-date">从 2023年4月23日 开始 💖</p>
                </div>
            </section>

            <!-- Birthday section -->
            <section class="birthday-section">
                <div class="birthday-cards">
                    <div class="birthday-card boy">
                        <div class="birthday-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h3><span class="name-yu">Yu</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">01月16日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="boy-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>

                    <div class="birthday-card girl">
                        <div class="birthday-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h3><span class="name-wang">Wang</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">04月15日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="girl-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Love quotes section -->
            <section class="love-quotes">
                <div class="quote-card">
                    <button class="quote-btn quote-btn-top-right" onclick="changeQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                    <i class="fas fa-quote-left quote-icon"></i>
                    <p class="quote-text" id="quote-text">爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。</p>
                </div>
            </section>

            <!-- Love Messages section -->
            <section class="love-messages-section">
                <h2>我们的爱情记录</h2>

                <!-- Message input form -->
                <div class="message-input-card">
                    <div class="message-form">
                        <div class="form-header">
                            <i class="fas fa-pen-fancy"></i>
                            <h3>写下爱的话语</h3>
                        </div>

                        <div class="author-and-template-row">
                            <div class="message-templates">
                                <button class="template-btn" onclick="showTemplates()">
                                    <i class="fas fa-magic"></i>
                                    选择浪漫模板
                                </button>
                            </div>
                            <div class="author-selection">
                                <label class="author-option">
                                    <input type="radio" name="author" value="Yu" checked>
                                    <span class="author-label yu">
                                        <i class="fas fa-heart"></i>
                                        Yu
                                    </span>
                                </label>
                                <label class="author-option">
                                    <input type="radio" name="author" value="Wang">
                                    <span class="author-label wang">
                                        <i class="fas fa-heart"></i>
                                        Wang
                                    </span>
                                </label>
                                <label class="author-option">
                                    <input type="radio" name="author" value="Other">
                                    <span class="author-label other">
                                        <i class="fas fa-heart"></i>
                                        Other
                                    </span>
                                </label>
                            </div>
                        </div>

                        <textarea id="messageText" placeholder="选择一个用户，然后写下你想说的话..."></textarea>

                        <div class="form-actions">
                            <button class="save-btn" onclick="saveMessage()">
                                <i class="fas fa-heart"></i>
                                保存爱的留言
                            </button>
                            <button class="clear-btn" onclick="clearMessage()">
                                <i class="fas fa-eraser"></i>
                                清空
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message templates modal -->
                <div id="templatesModal" class="templates-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>选择浪漫模板</h3>
                            <button class="close-btn" onclick="closeTemplates()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="templates-grid" id="templatesGrid">
                            <!-- Templates will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Messages display -->
                <div class="messages-container">
                    <div class="messages-header">
                        <h3>留言板</h3>
                        <div class="messages-actions">
                            <button class="collapse-btn" onclick="toggleMessagesCollapse()" title="折叠">
                                <i class="fas fa-chevron-down" id="collapseIcon"></i>
                            </button>
                            <button class="action-btn" onclick="exportMessages()">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                            <button class="action-btn" onclick="clearAllMessages()">
                                <i class="fas fa-trash"></i>
                                清空所有
                            </button>
                        </div>
                    </div>
                    <div class="messages-collapsible-content" id="messagesCollapsibleContent">
                        <div id="messagesList" class="messages-list">
                            <!-- Messages will be displayed here -->
                        </div>
                        <div id="emptyState" class="empty-state">
                            <i class="fas fa-heart-broken"></i>
                            <p>还没有留言，快来写下第一条爱的话语吧！</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Memory gallery -->
            <section class="memory-section">
                <h2>我们的美好回忆</h2>
                <div class="memory-grid">
                    <div class="memory-item" onclick="window.location.href='/together-days'">
                        <div class="memory-placeholder">
                            <i class="fas fa-calendar-alt"></i>
                            <p>在一起的日子</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href='/anniversary'">
                        <div class="memory-placeholder">
                            <i class="fas fa-star"></i>
                            <p>纪念日</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href='/memorial'">
                        <div class="memory-placeholder">
                            <i class="fas fa-gift"></i>
                            <p>纪念物</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href='/meetings'">
                        <div class="memory-placeholder">
                            <i class="fas fa-users"></i>
                            <p>每一次相遇</p>
                        </div>
                    </div>
                </div>
            </section>


        </main>

        <footer class="footer">
            <p>Made with <i class="fas fa-heart"></i> for our love story</p>
        </footer>
    </div>

    <script src="/config.js"></script>
    <script src="/device-detection-utils.js"></script>
    <script src="/cloudinary-setup.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/cloudinary-load-balancer.js"></script>
    <script src="/simple-video-manager.js"></script>
    <script src="/preload-manager.js"></script>
    <script src="/scripts/fix-video-resolution.js"></script>
    <script src="/scripts/emergency-video-fix.js"></script>
    <script src="/scripts/ultimate-video-fix.js"></script>
    <script src="/romantic-quotes.js" async></script>
    <script src="/modern-quotes-data.js" async></script>
    <script src="/script.js" defer></script>
    <script>
        // 立即执行的调试代码 - 确认页面版本
        console.log('🔥 页面脚本开始执行 - 版本: 2024-01-20-v2');
        console.log('🔍 当前document.readyState:', document.readyState);

        // 立即检查加载遮罩状态
        setTimeout(() => {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                console.log('🎭 加载遮罩状态:', {
                    exists: true,
                    hasHiddenClass: loadingOverlay.classList.contains('hidden'),
                    display: getComputedStyle(loadingOverlay).display,
                    opacity: getComputedStyle(loadingOverlay).opacity,
                    visibility: getComputedStyle(loadingOverlay).visibility
                });

                // 如果加载遮罩仍然显示，强制隐藏它
                if (!loadingOverlay.classList.contains('hidden')) {
                    console.log('🚨 强制隐藏加载遮罩');
                    loadingOverlay.classList.add('hidden');
                }
            } else {
                console.log('🎭 加载遮罩不存在');
            }
        }, 1000);

        // 简化的视频加载 - 增加强制超时保护
        // 初始化函数
        function initializePage() {
            console.log('📄 开始页面初始化');
            console.log('🔍 检查关键依赖:', {
                CONFIG: !!window.CONFIG,
                VideoManager: !!window.VideoManager,
                loadingOverlay: !!document.getElementById('loadingOverlay'),
                documentReady: document.readyState
            });

            // 强制超时保护 - 15秒后无论如何都隐藏加载界面
            const forceHideTimeout = setTimeout(() => {
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay && !loadingOverlay.classList.contains('hidden')) {
                    console.warn('⚠️ 强制超时，隐藏加载界面');
                    loadingOverlay.classList.add('hidden');
                    videoInitialized = true;
                    applyFallbackBackground();
                }
            }, 15000);

            // 直接加载首页视频
            console.log('🚀 准备调用 loadSimpleVideo');
            loadSimpleVideo().then(() => {
                console.log('✅ loadSimpleVideo 完成');
                clearTimeout(forceHideTimeout);
            }).catch((error) => {
                console.error('❌ 视频加载异常:', error);
                clearTimeout(forceHideTimeout);
            });
        }

        // 检查DOM状态并初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            // DOM已经加载完成，立即初始化
            console.log('📄 DOM已就绪，立即初始化');
            initializePage();
        }

        // 浪漫星星功能
        let currentQuoteCategory = 'confession';

        // 初始化浪漫星星
        function initRomanticStars() {
            const stars = document.querySelectorAll('.romantic-star');
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const category = this.getAttribute('data-quote-category');
                    currentQuoteCategory = category;
                    showRomanticQuote(category);
                });

                // 为每个星星添加随机位置变化功能
                initStarRandomPosition(star);
            });
        }

        // 为星星添加随机位置变化
        function initStarRandomPosition(star) {
            // 获取星星的动画持续时间
            const computedStyle = window.getComputedStyle(star);
            const animationDurations = computedStyle.animationDuration.split(',');
            const twinkleDuration = parseFloat(animationDurations[0]) * 1000; // 第一个动画（闪烁）的持续时间

            // 立即开始第一次位置变化循环
            schedulePositionChange(star, twinkleDuration);
        }

        // 安排位置变化
        function schedulePositionChange(star, duration) {
            // 在动画50%时（完全不透明时）改变位置
            setTimeout(() => {
                changeStarPosition(star);
            }, duration * 0.5);

            // 安排下一次位置变化
            setTimeout(() => {
                schedulePositionChange(star, duration);
            }, duration);
        }

        // 改变星星位置
        function changeStarPosition(star) {
            // 生成安全的随机位置（避免星星出现在边缘被裁剪）
            const minTop = 8;
            const maxTop = 85;
            const minLeft = 8;
            const maxLeft = 85;

            const randomTop = Math.random() * (maxTop - minTop) + minTop;
            const randomLeft = Math.random() * (maxLeft - minLeft) + minLeft;

            // 瞬间改变位置，不使用过渡效果（在完全透明时进行）
            star.style.transition = 'none';
            star.style.top = randomTop + '%';
            star.style.left = randomLeft + '%';

            // 恢复其他动画的过渡效果
            setTimeout(() => {
                star.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }, 50);
        }

        // 显示浪漫话语
        function showRomanticQuote(category) {
            const quote = getRandomQuoteByCategory(category);
            updateQuoteDisplay(quote);
            document.getElementById('romanticModal').style.display = 'flex';
        }

        // 获取新的话语
        function getNewQuote() {
            const quote = getRandomQuoteByCategory(currentQuoteCategory);
            updateQuoteDisplay(quote);
        }

        // 更新诗词显示
        function updateQuoteDisplay(quote) {
            const quoteTextElement = document.getElementById('romanticQuoteText');
            const quoteAuthorElement = document.getElementById('romanticQuoteAuthor');
            const quoteWorkElement = document.getElementById('romanticQuoteWork');

            // 添加淡出效果
            quoteTextElement.style.opacity = '0';
            quoteAuthorElement.style.opacity = '0';
            quoteWorkElement.style.opacity = '0';

            setTimeout(() => {
                if (typeof quote === 'object' && quote.text) {
                    // 处理诗词换行：在"。"后添加换行
                    const formattedText = quote.text.replace(/。/g, '。<br>');
                    quoteTextElement.innerHTML = formattedText;
                    quoteAuthorElement.textContent = '—— ' + quote.author;
                    quoteWorkElement.textContent = quote.work;
                } else {
                    // 兼容旧格式
                    const formattedText = quote.replace(/。/g, '。<br>');
                    quoteTextElement.innerHTML = formattedText;
                    quoteAuthorElement.textContent = '—— 佚名';
                    quoteWorkElement.textContent = '古典诗词';
                }

                // 强制设置颜色
                quoteTextElement.style.color = '#ec4899';
                quoteTextElement.style.fontWeight = '600';

                // 添加淡入动画
                quoteTextElement.classList.remove('quote-fade-in');
                quoteAuthorElement.classList.remove('quote-fade-in');
                quoteWorkElement.classList.remove('quote-fade-in');

                // 强制重排，然后添加动画类
                void quoteTextElement.offsetWidth;

                quoteTextElement.classList.add('quote-fade-in');
                quoteAuthorElement.classList.add('quote-fade-in');
                quoteWorkElement.classList.add('quote-fade-in');

                // 恢复透明度
                quoteTextElement.style.opacity = '1';
                quoteAuthorElement.style.opacity = '1';
                quoteWorkElement.style.opacity = '1';
            }, 200);
        }

        // 关闭浪漫话语模态框
        function closeRomanticModal() {
            document.getElementById('romanticModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('romanticModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRomanticModal();
            }
        });

        // 折叠/展开留言板功能
        let messagesCollapsed = false;

        function toggleMessagesCollapse() {
            const messagesCollapsibleContent = document.getElementById('messagesCollapsibleContent');
            const collapseIcon = document.getElementById('collapseIcon');

            messagesCollapsed = !messagesCollapsed;

            if (messagesCollapsed) {
                messagesCollapsibleContent.style.display = 'none';
                collapseIcon.className = 'fas fa-chevron-right';
            } else {
                messagesCollapsibleContent.style.display = 'block';
                collapseIcon.className = 'fas fa-chevron-down';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRomanticStars();

            // 确保留言板内容默认显示
            const messagesCollapsibleContent = document.getElementById('messagesCollapsibleContent');
            if (messagesCollapsibleContent) {
                messagesCollapsibleContent.style.display = 'block';
            }
        });

        // 简化的视频加载
        let videoInitialized = false;

        // 优化的视频加载函数 - 支持双重URL故障转移机制
        async function loadSimpleVideo() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const videoContainer = document.querySelector('.video-background');
            const oldVideo = videoContainer.querySelector('video');

            console.log('🎬 开始加载首页视频 - 优化版本');
            console.log('📋 检查必要元素:', {
                loadingOverlay: !!loadingOverlay,
                videoContainer: !!videoContainer,
                CONFIG: !!window.CONFIG,
                VideoManager: !!window.VideoManager,
                generateWorkingVideoUrl: !!window.generateWorkingVideoUrl,
                generateFallbackVideoUrl: !!window.generateFallbackVideoUrl
            });

            // 获取设备信息用于日志记录
            const deviceInfo = {
                userAgent: navigator.userAgent,
                screenWidth: window.innerWidth,
                screenHeight: window.innerHeight,
                connection: navigator.connection ? {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink
                } : null
            };
            console.log('📱 设备信息:', deviceInfo);

            // 优先使用修复后的URL格式
            if (window.generateWorkingVideoUrl) {
                console.log('🚨 使用修复后的URL格式加载首页视频');

                const startTime = performance.now();
                let attemptCount = 0;

                try {
                    // 生成主URL（带优化参数）
                    const primaryUrl = window.generateWorkingVideoUrl('INDEX');
                    // 生成备用URL（无参数）
                    const fallbackUrl = window.generateFallbackVideoUrl ? window.generateFallbackVideoUrl('INDEX') : null;

                    console.log('📹 生成的URL:', {
                        primary: primaryUrl,
                        fallback: fallbackUrl
                    });

                    if (primaryUrl) {
                        const video = document.createElement('video');
                        video.autoplay = true;
                        video.muted = true;
                        video.loop = true;
                        video.playsInline = true;
                        video.preload = 'auto';
                        video.id = 'background-video';
                        video.className = 'loaded';
                        video.style.opacity = '0';
                        video.style.transition = 'opacity 1s ease-in-out';

                        // 替换视频元素
                        if (oldVideo) {
                            videoContainer.removeChild(oldVideo);
                        }
                        videoContainer.appendChild(video);

                        // 设置加载超时处理
                        const loadTimeout = setTimeout(() => {
                            console.warn('⚠️ 视频加载超时 (20秒)');
                            if (fallbackUrl && video.src !== fallbackUrl) {
                                console.log('🔄 超时后尝试备用URL:', fallbackUrl);
                                video.src = fallbackUrl;
                                video.load();
                            } else {
                                console.error('❌ 加载超时且无备用URL可用');
                                loadVideoFallback();
                            }
                        }, 20000);

                        // 成功加载处理
                        video.addEventListener('canplaythrough', () => {
                            clearTimeout(loadTimeout);
                            const loadTime = performance.now() - startTime;

                            video.style.opacity = '1';
                            console.log(`✅ 视频加载成功 - 耗时: ${loadTime.toFixed(0)}ms, 尝试次数: ${attemptCount + 1}, URL: ${video.src}`);
                            videoInitialized = true;

                            // 记录成功状态
                            console.log('📊 加载状态追踪:', {
                                success: true,
                                loadTime: loadTime,
                                attempts: attemptCount + 1,
                                finalUrl: video.src,
                                isPrimaryUrl: video.src === primaryUrl,
                                deviceInfo: deviceInfo
                            });

                            // 隐藏加载界面
                            setTimeout(() => {
                                if (loadingOverlay && !loadingOverlay.classList.contains('hidden')) {
                                    loadingOverlay.classList.add('hidden');
                                    console.log('✅ 加载界面已隐藏');
                                }
                                startIntelligentPreloading();
                            }, 500);
                        });

                        // 错误处理 - 自动故障转移
                        video.addEventListener('error', (e) => {
                            attemptCount++;
                            const loadTime = performance.now() - startTime;

                            console.error(`❌ 视频加载失败 (尝试 ${attemptCount}):`, {
                                error: e.message || e.type || '未知错误',
                                currentUrl: video.src,
                                loadTime: loadTime,
                                deviceInfo: deviceInfo
                            });

                            // 如果是主URL失败且有备用URL，自动切换
                            if (video.src === primaryUrl && fallbackUrl) {
                                console.log('🔄 主URL失败，自动切换到备用URL:', fallbackUrl);
                                video.src = fallbackUrl;
                                video.load();
                                return; // 不要清除超时，给备用URL一个机会
                            }

                            // 如果备用URL也失败，或没有备用URL
                            clearTimeout(loadTimeout);
                            console.error('❌ 所有URL都失败，使用最终备用方案');

                            // 记录失败状态
                            console.log('📊 加载状态追踪:', {
                                success: false,
                                loadTime: loadTime,
                                attempts: attemptCount,
                                lastUrl: video.src,
                                error: e.message || e.type || '未知错误',
                                deviceInfo: deviceInfo
                            });

                            loadVideoFallback();
                        });

                        // 开始加载主URL
                        console.log('🚀 开始加载主URL:', primaryUrl);
                        video.src = primaryUrl;
                        video.load();
                        return;
                    }
                } catch (error) {
                    console.error('❌ URL生成失败:', error);
                }
            }

            // 如果修复后的URL不可用，尝试使用VideoManager
            console.log('⚠️ 修复后的URL不可用，尝试使用VideoManager');

            if (!window.CONFIG || !window.VideoManager) {
                console.warn('⚠️ 关键依赖缺失，使用最终备用方案');
                await loadVideoFallback();
                return;
            }

            try {
                const startTime = performance.now();

                // 获取视频配置
                const videoConfig = window.CONFIG.VIDEOS.PAGES.INDEX;
                console.log('📹 VideoManager视频配置:', videoConfig);

                if (!videoConfig) {
                    throw new Error('视频配置未找到');
                }

                // 等待VideoManager初始化
                if (!window.VideoManager.isInitialized) {
                    console.log('⏳ 等待VideoManager初始化...');
                    let attempts = 0;
                    while (!window.VideoManager.isInitialized && attempts < 50) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        attempts++;
                    }

                    if (!window.VideoManager.isInitialized) {
                        throw new Error('VideoManager初始化超时');
                    }
                }

                // 设置VideoManager加载超时
                const vmLoadTimeout = setTimeout(() => {
                    console.warn('⚠️ VideoManager加载超时 (15秒)');
                    loadVideoFallback();
                }, 15000);

                // 使用VideoManager加载视频
                console.log('🚀 使用VideoManager加载视频');
                const video = await window.VideoManager.loadVideo('INDEX', videoConfig);

                clearTimeout(vmLoadTimeout);
                const loadTime = performance.now() - startTime;

                if (video) {
                    // 替换视频元素
                    if (oldVideo) {
                        videoContainer.removeChild(oldVideo);
                    }

                    video.id = 'background-video';
                    video.className = 'loaded';
                    videoContainer.appendChild(video);

                    console.log(`✅ VideoManager视频加载成功 - 耗时: ${loadTime.toFixed(0)}ms`);
                    videoInitialized = true;

                    // 记录成功状态
                    console.log('📊 VideoManager加载状态追踪:', {
                        success: true,
                        loadTime: loadTime,
                        method: 'VideoManager',
                        deviceInfo: deviceInfo
                    });

                    // 隐藏加载界面
                    setTimeout(() => {
                        if (loadingOverlay && !loadingOverlay.classList.contains('hidden')) {
                            loadingOverlay.classList.add('hidden');
                            console.log('✅ 加载界面已隐藏 (VideoManager)');
                        }
                        startIntelligentPreloading();
                    }, 500);

                } else {
                    throw new Error('VideoManager返回空视频对象');
                }

            } catch (error) {
                console.error('❌ VideoManager加载失败:', {
                    error: error.message || error,
                    deviceInfo: deviceInfo
                });

                // 记录失败状态
                console.log('📊 VideoManager加载状态追踪:', {
                    success: false,
                    method: 'VideoManager',
                    error: error.message || error,
                    deviceInfo: deviceInfo
                });

                // 使用最终备用方案
                await loadVideoFallback();
            }
        }

        // 增强的备用视频加载方案
        async function loadVideoFallback() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const videoContainer = document.querySelector('.video-background');
            const oldVideo = videoContainer.querySelector('video');

            console.log('🔄 使用最终备用视频加载方案');
            const startTime = performance.now();

            // 获取设备信息
            const deviceInfo = {
                userAgent: navigator.userAgent,
                screenWidth: window.innerWidth,
                screenHeight: window.innerHeight
            };

            try {
                // 创建新的视频元素
                const video = document.createElement('video');
                video.autoplay = true;
                video.muted = true;
                video.loop = true;
                video.playsInline = true;
                video.preload = 'auto';
                video.id = 'background-video';
                video.className = 'loaded fallback';
                video.style.opacity = '0';
                video.style.transition = 'opacity 1s ease-in-out';

                // 设置本地视频源
                const source = document.createElement('source');
                source.src = '/background/home/<USER>';
                source.type = 'video/mp4';
                video.appendChild(source);

                console.log('📹 尝试加载本地视频:', source.src);

                // 替换视频元素
                if (oldVideo) {
                    videoContainer.removeChild(oldVideo);
                }
                videoContainer.appendChild(video);

                // 设置加载超时和错误处理
                const fallbackTimeout = setTimeout(() => {
                    console.warn('⚠️ 本地视频加载超时 (12秒)');
                    applyFallbackBackground();
                }, 12000);

                // 等待视频加载
                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', () => {
                        clearTimeout(fallbackTimeout);
                        const loadTime = performance.now() - startTime;

                        video.style.opacity = '1';
                        console.log(`✅ 本地视频加载成功 - 耗时: ${loadTime.toFixed(0)}ms`);

                        // 记录成功状态
                        console.log('📊 备用加载状态追踪:', {
                            success: true,
                            method: 'local-fallback',
                            loadTime: loadTime,
                            videoSrc: source.src,
                            deviceInfo: deviceInfo
                        });

                        resolve();
                    });

                    video.addEventListener('error', (e) => {
                        clearTimeout(fallbackTimeout);
                        const loadTime = performance.now() - startTime;

                        console.error('❌ 本地视频加载失败:', {
                            error: e.message || e.type || '未知错误',
                            videoSrc: source.src,
                            loadTime: loadTime,
                            deviceInfo: deviceInfo
                        });

                        // 记录失败状态
                        console.log('📊 备用加载状态追踪:', {
                            success: false,
                            method: 'local-fallback',
                            error: e.message || e.type || '未知错误',
                            loadTime: loadTime,
                            deviceInfo: deviceInfo
                        });

                        reject(e);
                    });

                    // 开始加载
                    video.load();
                });

            } catch (error) {
                console.error('❌ 所有视频加载方案都失败:', error);
                // 应用渐变背景作为最终备选
                applyFallbackBackground();
            }

            // 确保加载界面被隐藏
            setTimeout(() => {
                if (loadingOverlay && !loadingOverlay.classList.contains('hidden')) {
                    loadingOverlay.classList.add('hidden');
                    console.log('✅ 加载界面已隐藏 (备用方案)');
                }
                videoInitialized = true;
            }, 500);
        }

        // 增强的备用背景应用函数
        function applyFallbackBackground() {
            console.log('🎨 应用渐变背景作为最终备选方案');

            const theme = 'flower'; // 首页使用花朵主题
            const videoContainer = document.querySelector('.video-background');

            if (!videoContainer) {
                console.error('❌ 找不到视频容器元素');
                return;
            }

            try {
                // 尝试使用配置中的备用主题
                const fallbackConfig = window.CONFIG?.VIDEOS?.FALLBACK_THEMES?.[theme];

                if (fallbackConfig) {
                    console.log('🎨 使用配置的花朵主题渐变背景');
                    videoContainer.style.background = fallbackConfig.gradient;
                    videoContainer.style.animation = fallbackConfig.animation;
                    videoContainer.classList.add('fallback-active');
                } else {
                    // 如果配置不可用，使用硬编码的备用背景
                    console.log('🎨 使用硬编码的备用渐变背景');
                    videoContainer.style.background = `linear-gradient(135deg,
                        #fce7f3 0%, #fbcfe8 15%, #f9a8d4 30%, #f472b6 45%,
                        #ec4899 60%, #db2777 75%, #be185d 90%, #9d174d 100%)`;
                    videoContainer.style.animation = 'gradientShift 15s ease infinite';
                    videoContainer.classList.add('fallback-active');
                }

                // 隐藏任何残留的视频元素
                const videos = videoContainer.querySelectorAll('video');
                videos.forEach(video => {
                    video.style.display = 'none';
                });

                console.log('✅ 渐变背景应用成功');

                // 记录备用背景状态
                console.log('📊 备用背景状态追踪:', {
                    method: 'gradient-fallback',
                    theme: theme,
                    hasConfig: !!fallbackConfig,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('❌ 应用备用背景失败:', error);

                // 最终的最终备选方案 - 简单的纯色背景
                videoContainer.style.background = '#9d174d';
                videoContainer.classList.add('fallback-active');
                console.log('🎨 应用了最简单的纯色背景');
            }
        }





        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (videoInitialized && window.VideoManager) {
                if (document.hidden) {
                    console.log('📱 页面隐藏，暂停视频');
                    window.VideoManager.pauseAllVideos();
                } else {
                    console.log('📱 页面显示，恢复视频');
                    window.VideoManager.resumeCurrentVideo();
                }
            }
        });

        // 智能预加载启动函数
        async function startIntelligentPreloading() {
            try {
                console.log('🎯 准备启动智能视频预加载...');

                // 等待3秒后开始预加载，确保主页视频完全稳定
                setTimeout(async () => {
                    if (window.VideoManager && window.VideoManager.startPreloading) {
                        console.log('🚀 启动智能预加载...');
                        await window.VideoManager.startPreloading();

                        // 监听预加载完成事件
                        window.addEventListener('videoPreloaded', function(event) {
                            const { pageKey, duration } = event.detail;
                            console.log(`✅ 预加载完成: ${pageKey} (${duration.toFixed(0)}ms)`);
                        });

                        // 定期清理过期预加载缓存（每5分钟）
                        setInterval(() => {
                            if (window.VideoManager && window.VideoManager.cleanupPreloadCache) {
                                window.VideoManager.cleanupPreloadCache();
                            }
                        }, 5 * 60 * 1000);

                    } else {
                        console.warn('⚠️ VideoManager预加载功能不可用');
                    }
                }, 3000);

            } catch (error) {
                console.error('❌ 智能预加载启动失败:', error);
            }
        }

        // Service Worker注册 - 暂时禁用以解决缓存问题
        if (false && 'serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(function(registration) {
                        console.log('✅ Service Worker注册成功:', registration.scope);

                        // 监听Service Worker更新
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    console.log('🔄 Service Worker已更新，刷新页面以应用更改');
                                    // 可以在这里显示更新提示
                                }
                            });
                        });
                    })
                    .catch(function(error) {
                        console.error('❌ Service Worker注册失败:', error);
                    });
            });
        } else {
            console.log('⚠️ 浏览器不支持Service Worker');
        }
    </script>

    <!-- 页面导航优化 -->
    <script src="/page-navigation.js"></script>
</body>
</html>
