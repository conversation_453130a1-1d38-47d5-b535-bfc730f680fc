<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们美好爱情故事的专属网站，包含恋爱天数计算器和生日倒计时">
    <meta name="keywords" content="爱情, 情侣, 纪念日, 生日, 恋爱天数">
    <title>Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">

    <!-- 防止白色闪屏的关键CSS -->
    <style>
        html, body {
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4c1d95 75%,
                #6b21a8 100%
            ) !important;
            margin: 0;
            padding: 0;
        }
    </style>

    <!-- 配置文件 -->
    <script src="/config.js"></script>

    <!-- 简洁的2K视频背景系统 -->
    <script>
        console.log('🎬 初始化简洁2K视频背景系统...');
        
        // 全局视频配置 - 使用压缩版2K视频
        window.VIDEO_CONFIG = {
            enabled: true,
            videoUrl: '/background/home/<USER>',  // 8MB压缩版2K视频
            fallbackUrl: '/background/home/<USER>'           // 63MB原始2K视频作为备用
        };
        
        // 创建并加载2K视频背景
        function create2KVideoBackground() {
            console.log('📹 创建2K视频背景...');
            
            const container = document.getElementById('video-background-container');
            if (!container) {
                console.error('❌ 视频容器不存在');
                return;
            }
            
            // 创建视频元素
            const video = document.createElement('video');
            video.id = 'background-video';
            video.autoplay = true;
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = 'auto';
            
            // 设置视频样式
            video.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: -1;
            `;
            
            // 监听视频加载完成
            video.addEventListener('loadedmetadata', function() {
                console.log(`📊 视频分辨率: ${this.videoWidth}x${this.videoHeight}`);
                if (this.videoWidth >= 2560) {
                    console.log('✅ 2K视频加载成功！');
                } else {
                    console.warn(`⚠️ 视频分辨率 ${this.videoWidth}x${this.videoHeight} 不是2K`);
                }
                
                // 隐藏加载遮罩
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                }
            });
            
            // 监听视频错误
            video.addEventListener('error', function(e) {
                console.error('❌ 视频加载失败:', e);
                // 尝试备用视频
                if (this.src !== window.VIDEO_CONFIG.fallbackUrl) {
                    console.log('🔄 尝试备用视频...');
                    this.src = window.VIDEO_CONFIG.fallbackUrl;
                    this.load();
                } else {
                    console.error('❌ 备用视频也失败，使用渐变背景');
                    // 隐藏加载遮罩
                    const loadingOverlay = document.getElementById('loadingOverlay');
                    if (loadingOverlay) {
                        loadingOverlay.classList.add('hidden');
                    }
                }
            });
            
            // 设置视频源并添加到容器
            video.src = window.VIDEO_CONFIG.videoUrl;
            container.appendChild(video);
            
            console.log(`📹 视频元素已创建，源: ${video.src}`);
            
            return video;
        }
        
        // 全局测试函数
        window.showVideoInfo = function() {
            const videos = document.querySelectorAll('video');
            console.log('🎥 当前视频状态:');
            videos.forEach((video, index) => {
                console.log(`视频${index + 1}:`, {
                    URL: video.src,
                    分辨率: video.videoWidth && video.videoHeight ?
                        `${video.videoWidth}x${video.videoHeight}` : '未加载',
                    状态: video.readyState >= 4 ? '可播放' : '未就绪'
                });
            });
        };

        window.fix2KVideo = function() {
            console.log('🔧 强制修复为2K视频...');
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
                const newUrl = '/background/home/<USER>';
                console.log(`修复视频${index + 1}: ${newUrl}`);
                video.src = newUrl;
                video.load();
            });
        };
        
        // 页面加载完成后创建视频
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', create2KVideoBackground);
        } else {
            create2KVideoBackground();
        }
        
        console.log('✅ 2K视频背景系统初始化完成');
    </script>

    <!-- 动态样式管理器 -->
    <script src="/dynamic-styles.js"></script>

    <link rel="stylesheet" href="/style.css?v=green-buttons-020">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 自定义样式 -->
    <style>
        .message-date {
            font-family: 'Dancing Script', cursive !important;
            font-weight: 700 !important;
        }
        
        /* 留言板标题闪闪发光效果 */
        .messages-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        @keyframes sparkle {
            0%, 100% {
                background-position: 0% 50%;
                filter: brightness(1);
            }
            25% {
                background-position: 25% 50%;
                filter: brightness(1.2);
            }
            50% {
                background-position: 100% 50%;
                filter: brightness(1.5);
            }
            75% {
                background-position: 75% 50%;
                filter: brightness(1.2);
            }
        }
        
        /* 折叠按钮透明背景 */
        .collapse-btn {
            background: transparent !important;
            border: 2px solid rgba(103, 232, 249, 0.6) !important;
        }
        
        .collapse-btn:hover {
            background: rgba(103, 232, 249, 0.1) !important;
            border-color: #67e8f9 !important;
            color: #67e8f9 !important;
        }
        
        /* 增大留言内容的字体 */
        .message-content {
            font-size: 1.4rem !important;
            font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif !important;
        }
        
        /* 增大输入框的字体 */
        #messageText {
            font-size: 1.4rem !important;
            color: #d4af37 !important;
        }
        
        #messageText,
        #messageText:focus,
        #messageText:active,
        textarea#messageText {
            color: #d4af37 !important;
            caret-color: #d4af37 !important;
        }
        
        #messageText::placeholder {
            font-size: 1.4rem !important;
        }
        
        /* 修复倾斜效果 */
        .counter-card h2 {
            transform: rotate(0deg) !important;
        }
        
        .counter-card h2:hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text) {
            transform: rotate(0deg) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text):hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        /* "写下爱的话语"闪闪发光效果 */
        .form-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌸 加载2K花朵背景中...</div>
        <div class="loading-subtitle">正在为您准备美好的爱情主页</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 视频背景容器 - 将通过JavaScript动态创建 -->
    <div class="video-background" id="video-background-container">
        <!-- 2K视频将通过JavaScript动态插入 -->
    </div>

    <div class="container">
        <!-- 可点击的浪漫星星 -->
        <div class="romantic-stars">
            <div class="romantic-star star-variant-1" data-quote-category="confession" style="top: 15%; left: 20%;"></div>
            <div class="romantic-star star-variant-2" data-quote-category="sweet" style="top: 25%; left: 80%;"></div>
            <div class="romantic-star star-variant-3" data-quote-category="promise" style="top: 35%; left: 15%;"></div>
            <div class="romantic-star star-variant-4" data-quote-category="missing" style="top: 45%; left: 85%;"></div>
            <div class="romantic-star star-variant-5" data-quote-category="confession" style="top: 55%; left: 25%;"></div>
            <div class="romantic-star star-variant-6" data-quote-category="sweet" style="top: 65%; left: 75%;"></div>
            <div class="romantic-star star-variant-7" data-quote-category="promise" style="top: 75%; left: 10%;"></div>
            <div class="romantic-star star-variant-8" data-quote-category="missing" style="top: 85%; left: 90%;"></div>
        </div>

        <!-- Main content -->
        <header class="header">
            <h1 class="title">
                Yu
                <i class="fas fa-heart"></i>
                Wang
            </h1>
            <p class="subtitle">Forever and Always 💕</p>
        </header>

        <main class="main-content">
            <!-- Love counter section -->
            <section class="love-counter">
                <div class="counter-card">
                    <h2>我们在一起已经</h2>
                    <div class="counter-display">
                        <div class="counter-item">
                            <span class="counter-number" id="days">0</span>
                            <span class="counter-label">天</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="hours">0</span>
                            <span class="counter-label">小时</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="minutes">0</span>
                            <span class="counter-label">分钟</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="seconds">0</span>
                            <span class="counter-label">秒</span>
                        </div>
                    </div>
                    <p class="start-date">从 2023年4月23日 开始 💖</p>
                </div>
            </section>

            <!-- Birthday section -->
            <section class="birthday-section">
                <div class="birthday-cards">
                    <div class="birthday-card boy">
                        <div class="birthday-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h3><span class="name-yu">Yu</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">01月16日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="boy-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>

                    <div class="birthday-card girl">
                        <div class="birthday-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h3><span class="name-wang">Wang</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">04月15日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="girl-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Love quotes section -->
            <section class="love-quotes">
                <div class="quote-card">
                    <button class="quote-btn quote-btn-top-right" onclick="changeQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                    <i class="fas fa-quote-left quote-icon"></i>
                    <p class="quote-text" id="quote-text">爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。</p>
                </div>
            </section>

            <!-- Memory gallery -->
            <section class="memory-section">
                <h2>我们的美好回忆</h2>
                <div class="memory-grid">
                    <div class="memory-item" onclick="window.location.href='/together-days'">
                        <div class="memory-placeholder">
                            <i class="fas fa-calendar-alt"></i>
                            <p>在一起的日子</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href='/anniversary'">
                        <div class="memory-placeholder">
                            <i class="fas fa-star"></i>
                            <p>纪念日</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href='/memorial'">
                        <div class="memory-placeholder">
                            <i class="fas fa-gift"></i>
                            <p>纪念物</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href='/meetings'">
                        <div class="memory-placeholder">
                            <i class="fas fa-users"></i>
                            <p>每一次相遇</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>Made with <i class="fas fa-heart"></i> for our love story</p>
        </footer>
    </div>

    <script src="/romantic-quotes.js" async></script>
    <script src="/modern-quotes-data.js" async></script>
    <script src="/script.js" defer></script>
</body>
</html>
